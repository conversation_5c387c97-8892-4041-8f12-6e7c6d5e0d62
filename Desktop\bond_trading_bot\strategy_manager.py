"""
策略管理模块
负责策略的加载、管理、更新和执行
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from models import Strategy, Bond, BondType


class StrategyManager:
    """策略管理器"""
    
    def __init__(self, strategy_config_path: str = "strategies.json"):
        self.logger = logging.getLogger(__name__)
        self.strategy_config_path = strategy_config_path
        self.strategies: Dict[str, Strategy] = {}
        self.load_strategies()
    
    def load_strategies(self) -> None:
        """从配置文件加载策略"""
        try:
            if os.path.exists(self.strategy_config_path):
                with open(self.strategy_config_path, 'r', encoding='utf-8') as f:
                    strategies_data = json.load(f)
                
                for strategy_data in strategies_data:
                    strategy = self._create_strategy_from_dict(strategy_data)
                    if strategy:
                        self.strategies[strategy.id] = strategy
                
                self.logger.info(f"加载了 {len(self.strategies)} 个策略")
            else:
                # 创建默认策略
                self._create_default_strategies()
                self.save_strategies()
                
        except Exception as e:
            self.logger.error(f"加载策略失败: {e}")
            self._create_default_strategies()
    
    def _create_strategy_from_dict(self, data: Dict[str, Any]) -> Optional[Strategy]:
        """从字典创建策略对象"""
        try:
            # 解析债券类型列表
            bond_types = []
            for bond_type_str in data.get('bond_types', []):
                try:
                    bond_types.append(BondType(bond_type_str))
                except ValueError:
                    self.logger.warning(f"未知的债券类型: {bond_type_str}")
            
            strategy = Strategy(
                id=data['id'],
                name=data['name'],
                description=data['description'],
                bond_types=bond_types,
                min_yield=data.get('min_yield'),
                max_yield=data.get('max_yield'),
                min_duration=data.get('min_duration'),
                max_duration=data.get('max_duration'),
                min_credit_rating=data.get('min_credit_rating'),
                max_position=data.get('max_position'),
                risk_level=data.get('risk_level', 1),
                is_active=data.get('is_active', True),
                priority=data.get('priority', 1)
            )
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"创建策略失败: {e}")
            return None
    
    def _create_default_strategies(self) -> None:
        """创建默认策略"""
        default_strategies = [
            {
                'id': 'conservative_government',
                'name': '保守型国债策略',
                'description': '专注于低风险国债投资，追求稳定收益',
                'bond_types': [BondType.GOVERNMENT],
                'min_yield': 0.02,
                'max_yield': 0.06,
                'min_duration': 1.0,
                'max_duration': 10.0,
                'risk_level': 1,
                'priority': 1,
                'is_active': True
            },
            {
                'id': 'moderate_corporate',
                'name': '稳健型企业债策略',
                'description': '投资高评级企业债，平衡风险与收益',
                'bond_types': [BondType.CORPORATE, BondType.FINANCIAL],
                'min_yield': 0.03,
                'max_yield': 0.08,
                'min_duration': 2.0,
                'max_duration': 8.0,
                'min_credit_rating': 'AA',
                'risk_level': 2,
                'priority': 2,
                'is_active': True
            },
            {
                'id': 'aggressive_high_yield',
                'name': '激进型高收益策略',
                'description': '追求高收益，承担相应风险',
                'bond_types': [BondType.CORPORATE, BondType.CONVERTIBLE],
                'min_yield': 0.06,
                'max_yield': 0.15,
                'min_duration': 1.0,
                'max_duration': 15.0,
                'risk_level': 4,
                'priority': 3,
                'is_active': False  # 默认不激活高风险策略
            },
            {
                'id': 'short_term_liquidity',
                'name': '短期流动性策略',
                'description': '专注于短期债券，保持流动性',
                'bond_types': [BondType.GOVERNMENT, BondType.CENTRAL_BANK],
                'min_yield': 0.015,
                'max_yield': 0.04,
                'min_duration': 0.1,
                'max_duration': 2.0,
                'risk_level': 1,
                'priority': 4,
                'is_active': True
            }
        ]
        
        for strategy_data in default_strategies:
            strategy = Strategy(
                id=strategy_data['id'],
                name=strategy_data['name'],
                description=strategy_data['description'],
                bond_types=strategy_data['bond_types'],
                min_yield=strategy_data.get('min_yield'),
                max_yield=strategy_data.get('max_yield'),
                min_duration=strategy_data.get('min_duration'),
                max_duration=strategy_data.get('max_duration'),
                min_credit_rating=strategy_data.get('min_credit_rating'),
                risk_level=strategy_data['risk_level'],
                priority=strategy_data['priority'],
                is_active=strategy_data['is_active']
            )
            self.strategies[strategy.id] = strategy
    
    def save_strategies(self) -> None:
        """保存策略到配置文件"""
        try:
            strategies_data = []
            for strategy in self.strategies.values():
                strategy_dict = {
                    'id': strategy.id,
                    'name': strategy.name,
                    'description': strategy.description,
                    'bond_types': [bt.value for bt in strategy.bond_types],
                    'min_yield': strategy.min_yield,
                    'max_yield': strategy.max_yield,
                    'min_duration': strategy.min_duration,
                    'max_duration': strategy.max_duration,
                    'min_credit_rating': strategy.min_credit_rating,
                    'max_position': strategy.max_position,
                    'risk_level': strategy.risk_level,
                    'is_active': strategy.is_active,
                    'priority': strategy.priority
                }
                strategies_data.append(strategy_dict)
            
            with open(self.strategy_config_path, 'w', encoding='utf-8') as f:
                json.dump(strategies_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info("策略配置已保存")
            
        except Exception as e:
            self.logger.error(f"保存策略失败: {e}")
    
    def get_active_strategies(self) -> List[Strategy]:
        """获取所有激活的策略"""
        return [s for s in self.strategies.values() if s.is_active]
    
    def get_strategy(self, strategy_id: str) -> Optional[Strategy]:
        """根据ID获取策略"""
        return self.strategies.get(strategy_id)
    
    def add_strategy(self, strategy: Strategy) -> bool:
        """添加新策略"""
        try:
            if strategy.id in self.strategies:
                self.logger.warning(f"策略ID已存在: {strategy.id}")
                return False
            
            self.strategies[strategy.id] = strategy
            self.save_strategies()
            self.logger.info(f"添加策略成功: {strategy.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加策略失败: {e}")
            return False
    
    def update_strategy(self, strategy_id: str, **kwargs) -> bool:
        """更新策略参数"""
        try:
            if strategy_id not in self.strategies:
                self.logger.error(f"策略不存在: {strategy_id}")
                return False
            
            strategy = self.strategies[strategy_id]
            
            # 更新策略属性
            for key, value in kwargs.items():
                if hasattr(strategy, key):
                    setattr(strategy, key, value)
                else:
                    self.logger.warning(f"未知的策略属性: {key}")
            
            self.save_strategies()
            self.logger.info(f"更新策略成功: {strategy_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新策略失败: {e}")
            return False
    
    def activate_strategy(self, strategy_id: str) -> bool:
        """激活策略"""
        return self.update_strategy(strategy_id, is_active=True)
    
    def deactivate_strategy(self, strategy_id: str) -> bool:
        """停用策略"""
        return self.update_strategy(strategy_id, is_active=False)
    
    def remove_strategy(self, strategy_id: str) -> bool:
        """删除策略"""
        try:
            if strategy_id not in self.strategies:
                self.logger.error(f"策略不存在: {strategy_id}")
                return False
            
            del self.strategies[strategy_id]
            self.save_strategies()
            self.logger.info(f"删除策略成功: {strategy_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除策略失败: {e}")
            return False
    
    def find_matching_strategies(self, bond: Bond) -> List[Strategy]:
        """查找匹配债券的策略"""
        matching_strategies = []
        
        for strategy in self.get_active_strategies():
            if strategy.matches_bond(bond):
                matching_strategies.append(strategy)
        
        # 按优先级排序
        matching_strategies.sort(key=lambda s: s.priority)
        
        return matching_strategies
    
    def get_strategy_performance(self, strategy_id: str) -> Dict[str, Any]:
        """获取策略绩效统计"""
        # 这里可以实现策略绩效统计逻辑
        # 目前返回模拟数据
        return {
            'strategy_id': strategy_id,
            'total_trades': 0,
            'success_rate': 0.0,
            'total_return': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'last_update': datetime.now().isoformat()
        }
    
    def get_all_strategies_info(self) -> List[Dict[str, Any]]:
        """获取所有策略的信息"""
        strategies_info = []
        
        for strategy in self.strategies.values():
            info = {
                'id': strategy.id,
                'name': strategy.name,
                'description': strategy.description,
                'bond_types': [bt.value for bt in strategy.bond_types],
                'risk_level': strategy.risk_level,
                'priority': strategy.priority,
                'is_active': strategy.is_active,
                'performance': self.get_strategy_performance(strategy.id)
            }
            strategies_info.append(info)
        
        return strategies_info
