"""
现券交易机器人 - 数据模型
包含债券、策略、决策和状态等核心数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from enum import Enum
import json


class BondType(Enum):
    """债券类型枚举"""
    GOVERNMENT = "国债"
    CORPORATE = "企业债"
    FINANCIAL = "金融债"
    CONVERTIBLE = "可转债"
    LOCAL_GOVERNMENT = "地方政府债"
    CENTRAL_BANK = "央行票据"


class TradeDirection(Enum):
    """交易方向"""
    BUY = "买入"
    SELL = "卖出"
    HOLD = "持有"


class DecisionType(Enum):
    """决策类型"""
    QUOTE = "报价"
    INQUIRY = "询价"
    TRADE = "交易"
    WAIT = "等待"
    REJECT = "拒绝"


class BotState(Enum):
    """机器人状态"""
    IDLE = "空闲"
    ANALYZING = "分析中"
    TRADING = "交易中"
    WAITING = "等待中"
    ERROR = "错误"
    MAINTENANCE = "维护中"


@dataclass
class Bond:
    """债券信息数据类"""
    code: str  # 债券代码
    name: str  # 债券名称
    bond_type: BondType  # 债券类型
    face_value: float  # 面值
    coupon_rate: float  # 票面利率
    maturity_date: date  # 到期日
    issue_date: date  # 发行日
    current_price: Optional[float] = None  # 当前价格
    yield_rate: Optional[float] = None  # 收益率
    duration: Optional[float] = None  # 久期
    credit_rating: Optional[str] = None  # 信用评级
    issuer: Optional[str] = None  # 发行人
    trading_volume: Optional[float] = None  # 交易量
    last_update: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'code': self.code,
            'name': self.name,
            'bond_type': self.bond_type.value,
            'face_value': self.face_value,
            'coupon_rate': self.coupon_rate,
            'maturity_date': self.maturity_date.isoformat(),
            'issue_date': self.issue_date.isoformat(),
            'current_price': self.current_price,
            'yield_rate': self.yield_rate,
            'duration': self.duration,
            'credit_rating': self.credit_rating,
            'issuer': self.issuer,
            'trading_volume': self.trading_volume,
            'last_update': self.last_update.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Bond':
        """从字典创建债券对象"""
        return cls(
            code=data['code'],
            name=data['name'],
            bond_type=BondType(data['bond_type']),
            face_value=data['face_value'],
            coupon_rate=data['coupon_rate'],
            maturity_date=date.fromisoformat(data['maturity_date']),
            issue_date=date.fromisoformat(data['issue_date']),
            current_price=data.get('current_price'),
            yield_rate=data.get('yield_rate'),
            duration=data.get('duration'),
            credit_rating=data.get('credit_rating'),
            issuer=data.get('issuer'),
            trading_volume=data.get('trading_volume'),
            last_update=datetime.fromisoformat(data['last_update'])
        )


@dataclass
class Strategy:
    """交易策略数据类"""
    id: str  # 策略ID
    name: str  # 策略名称
    description: str  # 策略描述
    bond_types: List[BondType]  # 适用债券类型
    min_yield: Optional[float] = None  # 最小收益率
    max_yield: Optional[float] = None  # 最大收益率
    min_duration: Optional[float] = None  # 最小久期
    max_duration: Optional[float] = None  # 最大久期
    min_credit_rating: Optional[str] = None  # 最低信用评级
    max_position: Optional[float] = None  # 最大持仓
    risk_level: int = 1  # 风险等级 1-5
    is_active: bool = True  # 是否激活
    priority: int = 1  # 优先级
    created_time: datetime = field(default_factory=datetime.now)
    
    def matches_bond(self, bond: Bond) -> bool:
        """检查债券是否符合策略条件"""
        # 检查债券类型
        if bond.bond_type not in self.bond_types:
            return False
        
        # 检查收益率范围
        if bond.yield_rate is not None:
            if self.min_yield is not None and bond.yield_rate < self.min_yield:
                return False
            if self.max_yield is not None and bond.yield_rate > self.max_yield:
                return False
        
        # 检查久期范围
        if bond.duration is not None:
            if self.min_duration is not None and bond.duration < self.min_duration:
                return False
            if self.max_duration is not None and bond.duration > self.max_duration:
                return False
        
        return True


@dataclass
class Decision:
    """决策结果数据类"""
    decision_id: str  # 决策ID
    bond_code: str  # 债券代码
    decision_type: DecisionType  # 决策类型
    direction: TradeDirection  # 交易方向
    price: Optional[float] = None  # 价格
    quantity: Optional[float] = None  # 数量
    confidence: float = 0.0  # 置信度 0-1
    reason: str = ""  # 决策原因
    strategy_id: Optional[str] = None  # 使用的策略ID
    risk_score: float = 0.0  # 风险评分
    expected_return: Optional[float] = None  # 预期收益
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'decision_id': self.decision_id,
            'bond_code': self.bond_code,
            'decision_type': self.decision_type.value,
            'direction': self.direction.value,
            'price': self.price,
            'quantity': self.quantity,
            'confidence': self.confidence,
            'reason': self.reason,
            'strategy_id': self.strategy_id,
            'risk_score': self.risk_score,
            'expected_return': self.expected_return,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class BotStateInfo:
    """机器人状态信息"""
    state: BotState  # 当前状态
    last_update: datetime = field(default_factory=datetime.now)
    active_strategies: List[str] = field(default_factory=list)  # 激活的策略ID列表
    current_positions: Dict[str, float] = field(default_factory=dict)  # 当前持仓
    daily_pnl: float = 0.0  # 当日盈亏
    total_trades: int = 0  # 总交易次数
    success_rate: float = 0.0  # 成功率
    error_message: Optional[str] = None  # 错误信息
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'state': self.state.value,
            'last_update': self.last_update.isoformat(),
            'active_strategies': self.active_strategies,
            'current_positions': self.current_positions,
            'daily_pnl': self.daily_pnl,
            'total_trades': self.total_trades,
            'success_rate': self.success_rate,
            'error_message': self.error_message
        }


@dataclass
class MarketData:
    """市场数据"""
    timestamp: datetime
    bond_code: str
    bid_price: Optional[float] = None  # 买价
    ask_price: Optional[float] = None  # 卖价
    last_price: Optional[float] = None  # 最新价
    volume: Optional[float] = None  # 成交量
    yield_rate: Optional[float] = None  # 收益率
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'bond_code': self.bond_code,
            'bid_price': self.bid_price,
            'ask_price': self.ask_price,
            'last_price': self.last_price,
            'volume': self.volume,
            'yield_rate': self.yield_rate
        }
