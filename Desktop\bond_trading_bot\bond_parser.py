"""
现券解析模块
负责解析各种格式的现券数据，包括实时行情、历史数据等
"""

import re
import json
import pandas as pd
from datetime import datetime, date
from typing import Dict, List, Optional, Union, Any
import logging
from models import Bond, BondType, MarketData


class BondParser:
    """现券数据解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.bond_code_patterns = {
            'government': r'^[0-9]{6}$',  # 国债代码模式
            'corporate': r'^[0-9]{6}\.[A-Z]{2}$',  # 企业债代码模式
            'financial': r'^[0-9]{6}\.IB$',  # 金融债代码模式
        }
        
    def parse_bond_code(self, code: str) -> Optional[BondType]:
        """解析债券代码，判断债券类型"""
        try:
            code = code.strip().upper()
            
            # 国债判断
            if re.match(self.bond_code_patterns['government'], code):
                return BondType.GOVERNMENT
            
            # 企业债判断
            if re.match(self.bond_code_patterns['corporate'], code):
                return BondType.CORPORATE
            
            # 金融债判断
            if re.match(self.bond_code_patterns['financial'], code):
                return BondType.FINANCIAL
            
            # 可转债判断（通常以1开头）
            if code.startswith('1') and len(code) == 6:
                return BondType.CONVERTIBLE
            
            # 央行票据判断
            if 'CB' in code or '央票' in code:
                return BondType.CENTRAL_BANK
            
            # 地方政府债判断
            if '地方' in code or 'LOCAL' in code:
                return BondType.LOCAL_GOVERNMENT
            
            return BondType.CORPORATE  # 默认为企业债
            
        except Exception as e:
            self.logger.error(f"解析债券代码失败: {code}, 错误: {e}")
            return None
    
    def parse_bond_from_text(self, text: str) -> Optional[Bond]:
        """从文本中解析债券信息"""
        try:
            # 提取债券代码
            code_match = re.search(r'[0-9]{6}(?:\.[A-Z]{2})?', text)
            if not code_match:
                return None
            
            code = code_match.group()
            bond_type = self.parse_bond_code(code)
            
            # 提取债券名称
            name_patterns = [
                r'([^0-9\s]+(?:债|票据))',
                r'([A-Za-z\u4e00-\u9fff]+(?:债券|债|票据))',
            ]
            
            name = code  # 默认使用代码作为名称
            for pattern in name_patterns:
                name_match = re.search(pattern, text)
                if name_match:
                    name = name_match.group(1)
                    break
            
            # 提取价格信息
            price_match = re.search(r'价格[:：]?\s*([0-9]+\.?[0-9]*)', text)
            current_price = float(price_match.group(1)) if price_match else None
            
            # 提取收益率信息
            yield_patterns = [
                r'收益率[:：]?\s*([0-9]+\.?[0-9]*)%?',
                r'YTM[:：]?\s*([0-9]+\.?[0-9]*)%?',
            ]
            
            yield_rate = None
            for pattern in yield_patterns:
                yield_match = re.search(pattern, text)
                if yield_match:
                    yield_rate = float(yield_match.group(1))
                    if yield_rate > 1:  # 如果大于1，假设是百分比形式
                        yield_rate = yield_rate / 100
                    break
            
            # 提取票面利率
            coupon_match = re.search(r'票面利率[:：]?\s*([0-9]+\.?[0-9]*)%?', text)
            coupon_rate = float(coupon_match.group(1)) / 100 if coupon_match else 0.05
            
            # 创建债券对象
            bond = Bond(
                code=code,
                name=name,
                bond_type=bond_type,
                face_value=100.0,  # 默认面值
                coupon_rate=coupon_rate,
                maturity_date=date(2030, 12, 31),  # 默认到期日
                issue_date=date(2020, 1, 1),  # 默认发行日
                current_price=current_price,
                yield_rate=yield_rate
            )
            
            return bond
            
        except Exception as e:
            self.logger.error(f"从文本解析债券信息失败: {e}")
            return None
    
    def parse_bond_from_dict(self, data: Dict[str, Any]) -> Optional[Bond]:
        """从字典数据解析债券信息"""
        try:
            required_fields = ['code', 'name']
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"缺少必需字段: {field}")
            
            # 解析债券类型
            bond_type = BondType.CORPORATE  # 默认类型
            if 'bond_type' in data:
                if isinstance(data['bond_type'], str):
                    bond_type = BondType(data['bond_type'])
                else:
                    bond_type = data['bond_type']
            else:
                bond_type = self.parse_bond_code(data['code']) or BondType.CORPORATE
            
            # 解析日期
            maturity_date = date(2030, 12, 31)
            if 'maturity_date' in data:
                if isinstance(data['maturity_date'], str):
                    maturity_date = date.fromisoformat(data['maturity_date'])
                else:
                    maturity_date = data['maturity_date']
            
            issue_date = date(2020, 1, 1)
            if 'issue_date' in data:
                if isinstance(data['issue_date'], str):
                    issue_date = date.fromisoformat(data['issue_date'])
                else:
                    issue_date = data['issue_date']
            
            bond = Bond(
                code=data['code'],
                name=data['name'],
                bond_type=bond_type,
                face_value=data.get('face_value', 100.0),
                coupon_rate=data.get('coupon_rate', 0.05),
                maturity_date=maturity_date,
                issue_date=issue_date,
                current_price=data.get('current_price'),
                yield_rate=data.get('yield_rate'),
                duration=data.get('duration'),
                credit_rating=data.get('credit_rating'),
                issuer=data.get('issuer'),
                trading_volume=data.get('trading_volume')
            )
            
            return bond
            
        except Exception as e:
            self.logger.error(f"从字典解析债券信息失败: {e}")
            return None
    
    def parse_market_data(self, data: Union[str, Dict[str, Any]]) -> Optional[MarketData]:
        """解析市场数据"""
        try:
            if isinstance(data, str):
                # 从文本解析
                return self._parse_market_data_from_text(data)
            elif isinstance(data, dict):
                # 从字典解析
                return self._parse_market_data_from_dict(data)
            else:
                raise ValueError("不支持的数据格式")
                
        except Exception as e:
            self.logger.error(f"解析市场数据失败: {e}")
            return None
    
    def _parse_market_data_from_text(self, text: str) -> Optional[MarketData]:
        """从文本解析市场数据"""
        # 提取债券代码
        code_match = re.search(r'[0-9]{6}(?:\.[A-Z]{2})?', text)
        if not code_match:
            return None
        
        bond_code = code_match.group()
        
        # 提取价格信息
        bid_match = re.search(r'买价[:：]?\s*([0-9]+\.?[0-9]*)', text)
        ask_match = re.search(r'卖价[:：]?\s*([0-9]+\.?[0-9]*)', text)
        last_match = re.search(r'最新价[:：]?\s*([0-9]+\.?[0-9]*)', text)
        
        bid_price = float(bid_match.group(1)) if bid_match else None
        ask_price = float(ask_match.group(1)) if ask_match else None
        last_price = float(last_match.group(1)) if last_match else None
        
        # 提取成交量
        volume_match = re.search(r'成交量[:：]?\s*([0-9]+\.?[0-9]*)', text)
        volume = float(volume_match.group(1)) if volume_match else None
        
        # 提取收益率
        yield_match = re.search(r'收益率[:：]?\s*([0-9]+\.?[0-9]*)%?', text)
        yield_rate = float(yield_match.group(1)) if yield_match else None
        if yield_rate and yield_rate > 1:
            yield_rate = yield_rate / 100
        
        return MarketData(
            timestamp=datetime.now(),
            bond_code=bond_code,
            bid_price=bid_price,
            ask_price=ask_price,
            last_price=last_price,
            volume=volume,
            yield_rate=yield_rate
        )
    
    def _parse_market_data_from_dict(self, data: Dict[str, Any]) -> Optional[MarketData]:
        """从字典解析市场数据"""
        if 'bond_code' not in data:
            raise ValueError("缺少债券代码")
        
        timestamp = datetime.now()
        if 'timestamp' in data:
            if isinstance(data['timestamp'], str):
                timestamp = datetime.fromisoformat(data['timestamp'])
            else:
                timestamp = data['timestamp']
        
        return MarketData(
            timestamp=timestamp,
            bond_code=data['bond_code'],
            bid_price=data.get('bid_price'),
            ask_price=data.get('ask_price'),
            last_price=data.get('last_price'),
            volume=data.get('volume'),
            yield_rate=data.get('yield_rate')
        )
    
    def parse_excel_bonds(self, file_path: str) -> List[Bond]:
        """从Excel文件解析债券列表"""
        try:
            df = pd.read_excel(file_path)
            bonds = []
            
            for _, row in df.iterrows():
                bond_data = row.to_dict()
                bond = self.parse_bond_from_dict(bond_data)
                if bond:
                    bonds.append(bond)
            
            self.logger.info(f"从Excel文件解析了 {len(bonds)} 个债券")
            return bonds
            
        except Exception as e:
            self.logger.error(f"解析Excel文件失败: {e}")
            return []
    
    def validate_bond_data(self, bond: Bond) -> bool:
        """验证债券数据的完整性和合理性"""
        try:
            # 检查必需字段
            if not bond.code or not bond.name:
                return False
            
            # 检查价格合理性
            if bond.current_price is not None and bond.current_price <= 0:
                return False
            
            # 检查收益率合理性
            if bond.yield_rate is not None and (bond.yield_rate < 0 or bond.yield_rate > 1):
                return False
            
            # 检查票面利率合理性
            if bond.coupon_rate < 0 or bond.coupon_rate > 1:
                return False
            
            # 检查日期合理性
            if bond.maturity_date <= bond.issue_date:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证债券数据失败: {e}")
            return False
