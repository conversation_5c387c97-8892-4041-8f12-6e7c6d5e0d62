"""
状态管理器模块
负责管理机器人的运行状态、持仓信息、交易记录等
"""

import json
import os
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from models import BotState, BotStateInfo, Decision, Bond


class StateManager:
    """状态管理器"""
    
    def __init__(self, state_file_path: str = "bot_state.json"):
        self.logger = logging.getLogger(__name__)
        self.state_file_path = state_file_path
        self.lock = threading.Lock()
        
        # 初始化状态信息
        self.state_info = BotStateInfo(
            state=BotState.IDLE,
            active_strategies=[],
            current_positions={},
            daily_pnl=0.0,
            total_trades=0,
            success_rate=0.0
        )
        
        # 交易记录
        self.trade_history: List[Dict[str, Any]] = []
        self.decision_history: List[Decision] = []
        
        # 性能统计
        self.performance_stats = {
            'daily_stats': {},
            'monthly_stats': {},
            'total_stats': {}
        }
        
        # 加载状态
        self.load_state()
    
    def get_current_state(self) -> BotState:
        """获取当前状态"""
        with self.lock:
            return self.state_info.state
    
    def set_state(self, new_state: BotState, error_message: Optional[str] = None) -> None:
        """设置机器人状态"""
        with self.lock:
            old_state = self.state_info.state
            self.state_info.state = new_state
            self.state_info.last_update = datetime.now()
            self.state_info.error_message = error_message
            
            self.logger.info(f"状态变更: {old_state.value} -> {new_state.value}")
            
            if error_message:
                self.logger.error(f"状态错误: {error_message}")
            
            self.save_state()
    
    def get_state_info(self) -> BotStateInfo:
        """获取完整状态信息"""
        with self.lock:
            return self.state_info
    
    def update_active_strategies(self, strategy_ids: List[str]) -> None:
        """更新激活的策略列表"""
        with self.lock:
            self.state_info.active_strategies = strategy_ids.copy()
            self.state_info.last_update = datetime.now()
            self.save_state()
            
            self.logger.info(f"更新激活策略: {strategy_ids}")
    
    def get_current_positions(self) -> Dict[str, float]:
        """获取当前持仓"""
        with self.lock:
            return self.state_info.current_positions.copy()
    
    def update_position(self, bond_code: str, quantity: float, 
                       operation: str = 'set') -> None:
        """
        更新持仓信息
        
        Args:
            bond_code: 债券代码
            quantity: 数量
            operation: 操作类型 ('set', 'add', 'subtract')
        """
        with self.lock:
            current_quantity = self.state_info.current_positions.get(bond_code, 0.0)
            
            if operation == 'set':
                new_quantity = quantity
            elif operation == 'add':
                new_quantity = current_quantity + quantity
            elif operation == 'subtract':
                new_quantity = current_quantity - quantity
            else:
                raise ValueError(f"未知的操作类型: {operation}")
            
            if new_quantity <= 0:
                # 如果数量为0或负数，移除持仓
                if bond_code in self.state_info.current_positions:
                    del self.state_info.current_positions[bond_code]
            else:
                self.state_info.current_positions[bond_code] = new_quantity
            
            self.state_info.last_update = datetime.now()
            self.save_state()
            
            self.logger.info(f"更新持仓 {bond_code}: {current_quantity} -> {new_quantity}")
    
    def record_decision(self, decision: Decision) -> None:
        """记录决策"""
        with self.lock:
            self.decision_history.append(decision)
            
            # 只保留最近1000条决策记录
            if len(self.decision_history) > 1000:
                self.decision_history = self.decision_history[-1000:]
            
            self.logger.debug(f"记录决策: {decision.decision_id}")
    
    def record_trade(self, decision: Decision, bond: Bond, 
                    actual_price: float, actual_quantity: float,
                    success: bool = True) -> None:
        """
        记录交易
        
        Args:
            decision: 决策信息
            bond: 债券信息
            actual_price: 实际成交价格
            actual_quantity: 实际成交数量
            success: 是否成功
        """
        with self.lock:
            trade_record = {
                'trade_id': f"T{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'decision_id': decision.decision_id,
                'bond_code': bond.code,
                'bond_name': bond.name,
                'direction': decision.direction.value,
                'planned_price': decision.price,
                'actual_price': actual_price,
                'planned_quantity': decision.quantity,
                'actual_quantity': actual_quantity,
                'success': success,
                'timestamp': datetime.now().isoformat(),
                'strategy_id': decision.strategy_id,
                'pnl': self._calculate_trade_pnl(decision, actual_price, actual_quantity)
            }
            
            self.trade_history.append(trade_record)
            
            # 更新统计信息
            self.state_info.total_trades += 1
            if success:
                self._update_pnl(trade_record['pnl'])
            
            # 更新成功率
            successful_trades = len([t for t in self.trade_history if t['success']])
            self.state_info.success_rate = successful_trades / len(self.trade_history)
            
            # 更新持仓
            if success:
                if decision.direction.value == '买入':
                    self.update_position(bond.code, actual_quantity, 'add')
                elif decision.direction.value == '卖出':
                    self.update_position(bond.code, actual_quantity, 'subtract')
            
            self.state_info.last_update = datetime.now()
            self.save_state()
            
            self.logger.info(f"记录交易: {trade_record['trade_id']}")
    
    def _calculate_trade_pnl(self, decision: Decision, actual_price: float, 
                           actual_quantity: float) -> float:
        """计算交易盈亏"""
        if not decision.price:
            return 0.0
        
        price_diff = actual_price - decision.price
        
        if decision.direction.value == '买入':
            # 买入时，实际价格低于计划价格为正收益
            return -price_diff * actual_quantity
        else:
            # 卖出时，实际价格高于计划价格为正收益
            return price_diff * actual_quantity
    
    def _update_pnl(self, trade_pnl: float) -> None:
        """更新盈亏"""
        self.state_info.daily_pnl += trade_pnl
        
        # 更新日统计
        today = datetime.now().date().isoformat()
        if today not in self.performance_stats['daily_stats']:
            self.performance_stats['daily_stats'][today] = {
                'pnl': 0.0,
                'trades': 0,
                'success_trades': 0
            }
        
        self.performance_stats['daily_stats'][today]['pnl'] += trade_pnl
        self.performance_stats['daily_stats'][today]['trades'] += 1
    
    def get_daily_pnl(self, date: Optional[str] = None) -> float:
        """获取指定日期的盈亏"""
        if date is None:
            date = datetime.now().date().isoformat()
        
        return self.performance_stats['daily_stats'].get(date, {}).get('pnl', 0.0)
    
    def get_trade_history(self, limit: Optional[int] = None, 
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取交易历史
        
        Args:
            limit: 限制返回数量
            start_date: 开始日期
            end_date: 结束日期
        """
        with self.lock:
            filtered_history = self.trade_history.copy()
            
            # 日期过滤
            if start_date or end_date:
                filtered_history = []
                for trade in self.trade_history:
                    trade_time = datetime.fromisoformat(trade['timestamp'])
                    
                    if start_date and trade_time < start_date:
                        continue
                    if end_date and trade_time > end_date:
                        continue
                    
                    filtered_history.append(trade)
            
            # 限制数量
            if limit:
                filtered_history = filtered_history[-limit:]
            
            return filtered_history
    
    def get_decision_history(self, limit: Optional[int] = None) -> List[Decision]:
        """获取决策历史"""
        with self.lock:
            if limit:
                return self.decision_history[-limit:]
            return self.decision_history.copy()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self.lock:
            # 计算总体统计
            total_pnl = sum(trade['pnl'] for trade in self.trade_history)
            successful_trades = len([t for t in self.trade_history if t['success']])
            total_trades = len(self.trade_history)
            
            stats = {
                'total_trades': total_trades,
                'successful_trades': successful_trades,
                'success_rate': successful_trades / total_trades if total_trades > 0 else 0.0,
                'total_pnl': total_pnl,
                'daily_pnl': self.state_info.daily_pnl,
                'average_trade_pnl': total_pnl / total_trades if total_trades > 0 else 0.0,
                'current_positions_count': len(self.state_info.current_positions),
                'active_strategies_count': len(self.state_info.active_strategies),
                'last_update': self.state_info.last_update.isoformat()
            }
            
            return stats
    
    def reset_daily_stats(self) -> None:
        """重置日统计（通常在每日开始时调用）"""
        with self.lock:
            self.state_info.daily_pnl = 0.0
            self.state_info.last_update = datetime.now()
            self.save_state()
            
            self.logger.info("重置日统计")
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> None:
        """清理旧数据"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        with self.lock:
            # 清理交易历史
            self.trade_history = [
                trade for trade in self.trade_history
                if datetime.fromisoformat(trade['timestamp']) > cutoff_date
            ]
            
            # 清理决策历史
            self.decision_history = [
                decision for decision in self.decision_history
                if decision.timestamp > cutoff_date
            ]
            
            # 清理日统计
            cutoff_date_str = cutoff_date.date().isoformat()
            self.performance_stats['daily_stats'] = {
                date: stats for date, stats in self.performance_stats['daily_stats'].items()
                if date >= cutoff_date_str
            }
            
            self.save_state()
            self.logger.info(f"清理了{days_to_keep}天前的数据")
    
    def save_state(self) -> None:
        """保存状态到文件"""
        try:
            state_data = {
                'state_info': self.state_info.to_dict(),
                'trade_history': self.trade_history,
                'decision_history': [d.to_dict() for d in self.decision_history],
                'performance_stats': self.performance_stats,
                'last_save': datetime.now().isoformat()
            }
            
            # 原子写入
            temp_file = self.state_file_path + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
            
            # 替换原文件
            if os.path.exists(self.state_file_path):
                os.remove(self.state_file_path)
            os.rename(temp_file, self.state_file_path)
            
        except Exception as e:
            self.logger.error(f"保存状态失败: {e}")
    
    def load_state(self) -> None:
        """从文件加载状态"""
        try:
            if not os.path.exists(self.state_file_path):
                self.logger.info("状态文件不存在，使用默认状态")
                return
            
            with open(self.state_file_path, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 恢复状态信息
            if 'state_info' in state_data:
                state_dict = state_data['state_info']
                self.state_info = BotStateInfo(
                    state=BotState(state_dict['state']),
                    last_update=datetime.fromisoformat(state_dict['last_update']),
                    active_strategies=state_dict.get('active_strategies', []),
                    current_positions=state_dict.get('current_positions', {}),
                    daily_pnl=state_dict.get('daily_pnl', 0.0),
                    total_trades=state_dict.get('total_trades', 0),
                    success_rate=state_dict.get('success_rate', 0.0),
                    error_message=state_dict.get('error_message')
                )
            
            # 恢复交易历史
            self.trade_history = state_data.get('trade_history', [])
            
            # 恢复决策历史
            decision_dicts = state_data.get('decision_history', [])
            self.decision_history = []
            for d_dict in decision_dicts:
                try:
                    decision = Decision(
                        decision_id=d_dict['decision_id'],
                        bond_code=d_dict['bond_code'],
                        decision_type=DecisionType(d_dict['decision_type']),
                        direction=TradeDirection(d_dict['direction']),
                        price=d_dict.get('price'),
                        quantity=d_dict.get('quantity'),
                        confidence=d_dict.get('confidence', 0.0),
                        reason=d_dict.get('reason', ''),
                        strategy_id=d_dict.get('strategy_id'),
                        risk_score=d_dict.get('risk_score', 0.0),
                        expected_return=d_dict.get('expected_return'),
                        timestamp=datetime.fromisoformat(d_dict['timestamp'])
                    )
                    self.decision_history.append(decision)
                except Exception as e:
                    self.logger.warning(f"恢复决策记录失败: {e}")
            
            # 恢复性能统计
            self.performance_stats = state_data.get('performance_stats', {
                'daily_stats': {},
                'monthly_stats': {},
                'total_stats': {}
            })
            
            self.logger.info("状态加载成功")
            
        except Exception as e:
            self.logger.error(f"加载状态失败: {e}")
            # 使用默认状态
            self.state_info = BotStateInfo(state=BotState.IDLE)
    
    def export_data(self, file_path: str, data_type: str = 'all') -> bool:
        """
        导出数据
        
        Args:
            file_path: 导出文件路径
            data_type: 数据类型 ('all', 'trades', 'decisions', 'stats')
        """
        try:
            export_data = {}
            
            if data_type in ['all', 'trades']:
                export_data['trade_history'] = self.trade_history
            
            if data_type in ['all', 'decisions']:
                export_data['decision_history'] = [d.to_dict() for d in self.decision_history]
            
            if data_type in ['all', 'stats']:
                export_data['performance_stats'] = self.performance_stats
                export_data['state_info'] = self.state_info.to_dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"数据导出成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return False
