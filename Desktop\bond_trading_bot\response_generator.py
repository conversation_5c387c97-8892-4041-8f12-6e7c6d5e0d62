"""
话术生成器模块
负责根据决策结果生成相应的交易话术和回复
"""

import random
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from models import Bond, Decision, DecisionType, TradeDirection, Strategy


class ResponseGenerator:
    """话术生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.templates = self._load_response_templates()
        
    def _load_response_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """加载话术模板"""
        return {
            'quote': {
                'buy': [
                    "我们对{bond_name}({bond_code})有兴趣，当前报价{price}，收益率{yield_rate:.2f}%，您看如何？",
                    "关于{bond_name}，我们可以给出{price}的买入价格，这个价位您觉得合适吗？",
                    "我们看好{bond_name}的投资价值，愿意以{price}的价格买入，期待您的回复。",
                    "{bond_name}({bond_code})我们有需求，报价{price}，量{quantity}万，请确认。"
                ],
                'sell': [
                    "我们有{bond_name}({bond_code})的存货，报价{price}，收益率{yield_rate:.2f}%，您有兴趣吗？",
                    "向您推荐{bond_name}，我们的卖出价格是{price}，这是很有竞争力的价格。",
                    "我们可以提供{bond_name}，价格{price}，量{quantity}万，请考虑。",
                    "{bond_name}现货充足，报价{price}，收益率{yield_rate:.2f}%，欢迎询价。"
                ]
            },
            'inquiry': {
                'buy': [
                    "请问{bond_name}({bond_code})您有货吗？我们有买入需求。",
                    "想了解一下{bond_name}的市场情况，您那边有什么价位？",
                    "我们在寻找{bond_name}，请问您能提供什么价格？",
                    "询价{bond_name}({bond_code})，量{quantity}万左右，请报价。"
                ],
                'sell': [
                    "请问您对{bond_name}({bond_code})有需求吗？我们有现货。",
                    "想了解一下{bond_name}的需求情况，您那边什么价位有兴趣？",
                    "我们有{bond_name}可以提供，请问您的目标价位是多少？",
                    "询问{bond_name}({bond_code})需求，我们有{quantity}万现货。"
                ]
            },
            'trade': {
                'buy': [
                    "确认买入{bond_name}({bond_code})，价格{price}，数量{quantity}万，请安排交割。",
                    "我们决定买入{bond_name}，按照{price}的价格，量{quantity}万，谢谢。",
                    "成交！{bond_name}买入价{price}，数量{quantity}万，请确认交易详情。"
                ],
                'sell': [
                    "确认卖出{bond_name}({bond_code})，价格{price}，数量{quantity}万，请安排交割。",
                    "我们决定卖出{bond_name}，按照{price}的价格，量{quantity}万，谢谢。",
                    "成交！{bond_name}卖出价{price}，数量{quantity}万，请确认交易详情。"
                ]
            },
            'wait': [
                "我们正在评估{bond_name}({bond_code})的投资机会，稍后会给您回复。",
                "关于{bond_name}，我们需要进一步分析，请稍等。",
                "我们对{bond_name}持观望态度，暂时不做操作。",
                "目前{bond_name}的市场条件不太符合我们的策略，继续观察中。"
            ],
            'reject': [
                "很抱歉，{bond_name}({bond_code})不符合我们当前的投资策略。",
                "感谢您的推荐，但{bond_name}暂时不在我们的投资范围内。",
                "我们暂时对{bond_name}没有兴趣，谢谢理解。",
                "由于风险控制要求，我们暂不考虑{bond_name}。"
            ],
            'greeting': [
                "您好！我是现券交易机器人，很高兴为您服务。",
                "早上好！今天有什么债券交易需求吗？",
                "您好！我可以为您提供债券报价和交易服务。"
            ],
            'closing': [
                "感谢您的咨询，期待下次合作！",
                "如有其他需求，请随时联系我们。",
                "祝您交易愉快！"
            ]
        }
    
    def generate_response(self, decision: Decision, bond: Bond, 
                         strategy: Optional[Strategy] = None,
                         context: Optional[Dict[str, Any]] = None) -> str:
        """
        根据决策生成话术
        
        Args:
            decision: 决策结果
            bond: 债券信息
            strategy: 使用的策略
            context: 额外上下文信息
            
        Returns:
            str: 生成的话术
        """
        try:
            # 根据决策类型选择模板
            if decision.decision_type == DecisionType.QUOTE:
                return self._generate_quote_response(decision, bond, strategy, context)
            elif decision.decision_type == DecisionType.INQUIRY:
                return self._generate_inquiry_response(decision, bond, strategy, context)
            elif decision.decision_type == DecisionType.TRADE:
                return self._generate_trade_response(decision, bond, strategy, context)
            elif decision.decision_type == DecisionType.WAIT:
                return self._generate_wait_response(decision, bond, strategy, context)
            elif decision.decision_type == DecisionType.REJECT:
                return self._generate_reject_response(decision, bond, strategy, context)
            else:
                return self._generate_default_response(bond)
                
        except Exception as e:
            self.logger.error(f"生成话术失败: {e}")
            return f"关于{bond.name}({bond.code})，我们正在处理中，请稍后。"
    
    def _generate_quote_response(self, decision: Decision, bond: Bond, 
                               strategy: Optional[Strategy] = None,
                               context: Optional[Dict[str, Any]] = None) -> str:
        """生成报价话术"""
        direction = 'buy' if decision.direction == TradeDirection.BUY else 'sell'
        templates = self.templates['quote'][direction]
        template = random.choice(templates)
        
        # 准备模板变量
        variables = self._prepare_template_variables(decision, bond, strategy, context)
        
        try:
            response = template.format(**variables)
            
            # 添加置信度和风险信息（如果需要）
            if decision.confidence < 0.7:
                response += " (建议谨慎考虑)"
            
            # 添加策略信息
            if strategy and context and context.get('include_strategy_info'):
                response += f" [策略: {strategy.name}]"
            
            return response
            
        except KeyError as e:
            self.logger.warning(f"模板变量缺失: {e}")
            return self._generate_fallback_response(decision, bond)
    
    def _generate_inquiry_response(self, decision: Decision, bond: Bond, 
                                 strategy: Optional[Strategy] = None,
                                 context: Optional[Dict[str, Any]] = None) -> str:
        """生成询价话术"""
        direction = 'buy' if decision.direction == TradeDirection.BUY else 'sell'
        templates = self.templates['inquiry'][direction]
        template = random.choice(templates)
        
        variables = self._prepare_template_variables(decision, bond, strategy, context)
        
        try:
            return template.format(**variables)
        except KeyError as e:
            self.logger.warning(f"模板变量缺失: {e}")
            return self._generate_fallback_response(decision, bond)
    
    def _generate_trade_response(self, decision: Decision, bond: Bond, 
                               strategy: Optional[Strategy] = None,
                               context: Optional[Dict[str, Any]] = None) -> str:
        """生成交易确认话术"""
        direction = 'buy' if decision.direction == TradeDirection.BUY else 'sell'
        templates = self.templates['trade'][direction]
        template = random.choice(templates)
        
        variables = self._prepare_template_variables(decision, bond, strategy, context)
        
        try:
            response = template.format(**variables)
            
            # 添加交易时间
            response += f" [交易时间: {datetime.now().strftime('%H:%M:%S')}]"
            
            return response
            
        except KeyError as e:
            self.logger.warning(f"模板变量缺失: {e}")
            return self._generate_fallback_response(decision, bond)
    
    def _generate_wait_response(self, decision: Decision, bond: Bond, 
                              strategy: Optional[Strategy] = None,
                              context: Optional[Dict[str, Any]] = None) -> str:
        """生成等待话术"""
        templates = self.templates['wait']
        template = random.choice(templates)
        
        variables = self._prepare_template_variables(decision, bond, strategy, context)
        
        try:
            response = template.format(**variables)
            
            # 添加原因说明
            if decision.reason:
                response += f" 原因: {decision.reason}"
            
            return response
            
        except KeyError as e:
            self.logger.warning(f"模板变量缺失: {e}")
            return self._generate_fallback_response(decision, bond)
    
    def _generate_reject_response(self, decision: Decision, bond: Bond, 
                                strategy: Optional[Strategy] = None,
                                context: Optional[Dict[str, Any]] = None) -> str:
        """生成拒绝话术"""
        templates = self.templates['reject']
        template = random.choice(templates)
        
        variables = self._prepare_template_variables(decision, bond, strategy, context)
        
        try:
            response = template.format(**variables)
            
            # 添加拒绝原因
            if decision.reason:
                response += f" ({decision.reason})"
            
            return response
            
        except KeyError as e:
            self.logger.warning(f"模板变量缺失: {e}")
            return self._generate_fallback_response(decision, bond)
    
    def _prepare_template_variables(self, decision: Decision, bond: Bond, 
                                  strategy: Optional[Strategy] = None,
                                  context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """准备模板变量"""
        variables = {
            'bond_name': bond.name,
            'bond_code': bond.code,
            'price': decision.price or bond.current_price or 100.0,
            'quantity': (decision.quantity or 100000) / 10000,  # 转换为万元
            'yield_rate': (bond.yield_rate or 0.05) * 100,  # 转换为百分比
            'face_value': bond.face_value,
            'coupon_rate': bond.coupon_rate * 100,
            'issuer': bond.issuer or '未知发行人',
            'credit_rating': bond.credit_rating or '未评级',
            'maturity_date': bond.maturity_date.strftime('%Y-%m-%d'),
            'confidence': decision.confidence * 100,
            'risk_score': decision.risk_score * 100
        }
        
        # 添加策略信息
        if strategy:
            variables.update({
                'strategy_name': strategy.name,
                'strategy_description': strategy.description,
                'risk_level': strategy.risk_level
            })
        
        # 添加上下文信息
        if context:
            variables.update(context)
        
        return variables
    
    def _generate_fallback_response(self, decision: Decision, bond: Bond) -> str:
        """生成备用话术"""
        if decision.direction == TradeDirection.BUY:
            return f"我们对{bond.name}({bond.code})有买入兴趣，请联系我们详谈。"
        elif decision.direction == TradeDirection.SELL:
            return f"我们有{bond.name}({bond.code})可以提供，请联系我们详谈。"
        else:
            return f"关于{bond.name}({bond.code})，我们正在评估中。"
    
    def _generate_default_response(self, bond: Bond) -> str:
        """生成默认话术"""
        return f"感谢您咨询{bond.name}({bond.code})，我们会尽快给您回复。"
    
    def generate_greeting(self, context: Optional[Dict[str, Any]] = None) -> str:
        """生成问候语"""
        templates = self.templates['greeting']
        greeting = random.choice(templates)
        
        # 根据时间调整问候语
        current_hour = datetime.now().hour
        if 6 <= current_hour < 12:
            time_greeting = "早上好！"
        elif 12 <= current_hour < 18:
            time_greeting = "下午好！"
        else:
            time_greeting = "晚上好！"
        
        return greeting.replace("您好！", time_greeting)
    
    def generate_closing(self, context: Optional[Dict[str, Any]] = None) -> str:
        """生成结束语"""
        templates = self.templates['closing']
        return random.choice(templates)
    
    def generate_market_summary(self, bonds: List[Bond], 
                              decisions: List[Decision]) -> str:
        """生成市场总结话术"""
        if not bonds:
            return "今日暂无债券交易活动。"
        
        # 统计信息
        total_bonds = len(bonds)
        buy_decisions = len([d for d in decisions if d.direction == TradeDirection.BUY])
        sell_decisions = len([d for d in decisions if d.direction == TradeDirection.SELL])
        
        # 平均收益率
        yields = [b.yield_rate for b in bonds if b.yield_rate]
        avg_yield = sum(yields) / len(yields) if yields else 0
        
        summary = f"今日关注{total_bonds}只债券，"
        
        if buy_decisions > 0:
            summary += f"其中{buy_decisions}只有买入意向，"
        
        if sell_decisions > 0:
            summary += f"{sell_decisions}只有卖出意向，"
        
        if avg_yield > 0:
            summary += f"平均收益率{avg_yield*100:.2f}%。"
        
        summary += "欢迎随时咨询交易需求。"
        
        return summary
    
    def generate_risk_warning(self, decision: Decision, bond: Bond) -> Optional[str]:
        """生成风险提示"""
        warnings = []
        
        # 高风险警告
        if decision.risk_score > 0.7:
            warnings.append("该债券风险较高，请谨慎投资")
        
        # 低置信度警告
        if decision.confidence < 0.5:
            warnings.append("决策置信度较低，建议进一步分析")
        
        # 信用风险警告
        if bond.credit_rating and bond.credit_rating.startswith('B'):
            warnings.append("该债券信用评级较低，存在违约风险")
        
        # 流动性风险警告
        if bond.trading_volume and bond.trading_volume < 10000:
            warnings.append("该债券流动性较差，可能影响交易")
        
        if warnings:
            return "风险提示: " + "; ".join(warnings)
        
        return None
    
    def customize_response(self, base_response: str, 
                          customization: Dict[str, Any]) -> str:
        """自定义话术"""
        response = base_response
        
        # 添加客户特定信息
        if 'client_name' in customization:
            response = f"{customization['client_name']}，{response}"
        
        # 添加紧急标识
        if customization.get('urgent', False):
            response = f"[紧急] {response}"
        
        # 添加特殊说明
        if 'note' in customization:
            response += f" 备注: {customization['note']}"
        
        return response
