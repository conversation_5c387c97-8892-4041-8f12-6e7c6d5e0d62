"""
决策引擎模块
负责分析市场数据，结合策略进行交易决策
"""

import uuid
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from models import Bond, Strategy, Decision, DecisionType, TradeDirection, MarketData


class DecisionEngine:
    """交易决策引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.risk_free_rate = 0.025  # 无风险利率
        self.market_volatility = 0.15  # 市场波动率
        self.max_position_ratio = 0.1  # 最大单一持仓比例
        
    def make_decision(self, bond: Bond, strategies: List[Strategy], 
                     market_data: Optional[MarketData] = None,
                     current_positions: Optional[Dict[str, float]] = None) -> Optional[Decision]:
        """
        基于债券信息、策略和市场数据做出交易决策
        
        Args:
            bond: 债券信息
            strategies: 匹配的策略列表
            market_data: 市场数据
            current_positions: 当前持仓情况
            
        Returns:
            Decision: 决策结果
        """
        try:
            if not strategies:
                return self._create_wait_decision(bond.code, "没有匹配的策略")
            
            # 选择最优策略
            best_strategy = self._select_best_strategy(bond, strategies, market_data)
            
            # 风险评估
            risk_score = self._calculate_risk_score(bond, market_data)
            
            # 检查风险阈值
            if risk_score > best_strategy.risk_level * 0.2:  # 风险阈值
                return self._create_reject_decision(
                    bond.code, 
                    f"风险评分过高: {risk_score:.2f}",
                    best_strategy.id,
                    risk_score
                )
            
            # 检查持仓限制
            if current_positions and self._check_position_limit(bond.code, current_positions):
                return self._create_wait_decision(
                    bond.code, 
                    "持仓已达上限",
                    best_strategy.id
                )
            
            # 价格分析
            price_analysis = self._analyze_price(bond, market_data)
            
            # 生成交易决策
            decision = self._generate_trade_decision(
                bond, best_strategy, price_analysis, risk_score, market_data
            )
            
            return decision
            
        except Exception as e:
            self.logger.error(f"决策生成失败: {e}")
            return self._create_wait_decision(bond.code, f"决策错误: {str(e)}")
    
    def _select_best_strategy(self, bond: Bond, strategies: List[Strategy], 
                            market_data: Optional[MarketData] = None) -> Strategy:
        """选择最优策略"""
        if len(strategies) == 1:
            return strategies[0]
        
        # 计算每个策略的得分
        strategy_scores = []
        
        for strategy in strategies:
            score = 0
            
            # 优先级得分（优先级越高得分越高）
            score += (10 - strategy.priority) * 10
            
            # 风险匹配得分
            if bond.yield_rate:
                expected_risk = self._estimate_bond_risk(bond)
                risk_match = 1 - abs(expected_risk - strategy.risk_level * 0.2)
                score += risk_match * 20
            
            # 收益率匹配得分
            if bond.yield_rate and strategy.min_yield and strategy.max_yield:
                if strategy.min_yield <= bond.yield_rate <= strategy.max_yield:
                    score += 30
                else:
                    # 收益率越接近范围得分越高
                    distance = min(
                        abs(bond.yield_rate - strategy.min_yield),
                        abs(bond.yield_rate - strategy.max_yield)
                    )
                    score += max(0, 30 - distance * 100)
            
            strategy_scores.append((strategy, score))
        
        # 返回得分最高的策略
        strategy_scores.sort(key=lambda x: x[1], reverse=True)
        return strategy_scores[0][0]
    
    def _calculate_risk_score(self, bond: Bond, market_data: Optional[MarketData] = None) -> float:
        """计算风险评分 (0-1)"""
        risk_score = 0.0
        
        # 信用风险
        credit_risk = self._assess_credit_risk(bond)
        risk_score += credit_risk * 0.4
        
        # 利率风险（基于久期）
        interest_rate_risk = self._assess_interest_rate_risk(bond)
        risk_score += interest_rate_risk * 0.3
        
        # 流动性风险
        liquidity_risk = self._assess_liquidity_risk(bond, market_data)
        risk_score += liquidity_risk * 0.2
        
        # 市场风险
        market_risk = self._assess_market_risk(bond, market_data)
        risk_score += market_risk * 0.1
        
        return min(risk_score, 1.0)
    
    def _assess_credit_risk(self, bond: Bond) -> float:
        """评估信用风险"""
        if bond.credit_rating:
            rating_risk_map = {
                'AAA': 0.05, 'AA+': 0.08, 'AA': 0.12, 'AA-': 0.15,
                'A+': 0.20, 'A': 0.25, 'A-': 0.30,
                'BBB+': 0.40, 'BBB': 0.50, 'BBB-': 0.60,
                'BB+': 0.70, 'BB': 0.80, 'BB-': 0.85,
                'B+': 0.90, 'B': 0.95, 'B-': 1.0
            }
            return rating_risk_map.get(bond.credit_rating, 0.5)
        
        # 根据债券类型估算信用风险
        type_risk_map = {
            'government': 0.05,
            'central_bank': 0.08,
            'financial': 0.15,
            'local_government': 0.20,
            'corporate': 0.35,
            'convertible': 0.45
        }
        
        return type_risk_map.get(bond.bond_type.name.lower(), 0.5)
    
    def _assess_interest_rate_risk(self, bond: Bond) -> float:
        """评估利率风险"""
        if bond.duration:
            # 久期越长，利率风险越高
            return min(bond.duration / 20.0, 1.0)
        
        # 根据到期时间估算久期
        if bond.maturity_date:
            years_to_maturity = (bond.maturity_date - datetime.now().date()).days / 365.25
            estimated_duration = years_to_maturity * 0.8  # 简化估算
            return min(estimated_duration / 20.0, 1.0)
        
        return 0.3  # 默认中等风险
    
    def _assess_liquidity_risk(self, bond: Bond, market_data: Optional[MarketData] = None) -> float:
        """评估流动性风险"""
        if market_data and market_data.volume:
            # 成交量越大，流动性风险越低
            if market_data.volume > 1000000:  # 高流动性
                return 0.1
            elif market_data.volume > 100000:  # 中等流动性
                return 0.3
            else:  # 低流动性
                return 0.7
        
        # 根据债券类型估算流动性风险
        type_liquidity_map = {
            'government': 0.1,
            'central_bank': 0.15,
            'financial': 0.25,
            'local_government': 0.35,
            'corporate': 0.45,
            'convertible': 0.55
        }
        
        return type_liquidity_map.get(bond.bond_type.name.lower(), 0.4)
    
    def _assess_market_risk(self, bond: Bond, market_data: Optional[MarketData] = None) -> float:
        """评估市场风险"""
        # 基于市场波动率的简化计算
        base_risk = self.market_volatility
        
        # 如果有市场数据，可以进行更精确的计算
        if market_data and market_data.bid_price and market_data.ask_price:
            spread = abs(market_data.ask_price - market_data.bid_price)
            spread_ratio = spread / ((market_data.ask_price + market_data.bid_price) / 2)
            base_risk += spread_ratio * 2  # 价差越大，市场风险越高
        
        return min(base_risk, 1.0)
    
    def _estimate_bond_risk(self, bond: Bond) -> float:
        """估算债券整体风险水平"""
        return self._calculate_risk_score(bond, None)
    
    def _check_position_limit(self, bond_code: str, current_positions: Dict[str, float]) -> bool:
        """检查持仓限制"""
        current_position = current_positions.get(bond_code, 0)
        # 这里可以实现更复杂的持仓限制逻辑
        return current_position >= 1000000  # 简化的持仓上限
    
    def _analyze_price(self, bond: Bond, market_data: Optional[MarketData] = None) -> Dict[str, Any]:
        """分析价格情况"""
        analysis = {
            'fair_value': None,
            'current_price': bond.current_price,
            'price_deviation': 0.0,
            'recommendation': 'HOLD'
        }
        
        # 计算理论公允价值（简化模型）
        if bond.yield_rate and bond.coupon_rate:
            fair_value = self._calculate_fair_value(bond)
            analysis['fair_value'] = fair_value
            
            if bond.current_price:
                deviation = (bond.current_price - fair_value) / fair_value
                analysis['price_deviation'] = deviation
                
                # 价格建议
                if deviation < -0.02:  # 低估超过2%
                    analysis['recommendation'] = 'BUY'
                elif deviation > 0.02:  # 高估超过2%
                    analysis['recommendation'] = 'SELL'
        
        return analysis
    
    def _calculate_fair_value(self, bond: Bond) -> float:
        """计算债券公允价值（简化模型）"""
        if not bond.yield_rate or not bond.coupon_rate:
            return bond.face_value
        
        # 简化的债券定价模型
        years_to_maturity = (bond.maturity_date - datetime.now().date()).days / 365.25
        
        # 现金流折现
        annual_coupon = bond.face_value * bond.coupon_rate
        present_value = 0
        
        # 计算每年利息的现值
        for year in range(1, int(years_to_maturity) + 1):
            present_value += annual_coupon / ((1 + bond.yield_rate) ** year)
        
        # 加上到期本金的现值
        present_value += bond.face_value / ((1 + bond.yield_rate) ** years_to_maturity)
        
        return present_value
    
    def _generate_trade_decision(self, bond: Bond, strategy: Strategy, 
                               price_analysis: Dict[str, Any], risk_score: float,
                               market_data: Optional[MarketData] = None) -> Decision:
        """生成交易决策"""
        decision_id = str(uuid.uuid4())
        
        # 基于价格分析确定交易方向
        recommendation = price_analysis.get('recommendation', 'HOLD')
        
        if recommendation == 'BUY':
            direction = TradeDirection.BUY
            decision_type = DecisionType.QUOTE
        elif recommendation == 'SELL':
            direction = TradeDirection.SELL
            decision_type = DecisionType.QUOTE
        else:
            direction = TradeDirection.HOLD
            decision_type = DecisionType.WAIT
        
        # 计算置信度
        confidence = self._calculate_confidence(bond, strategy, price_analysis, risk_score)
        
        # 计算建议价格和数量
        price = self._calculate_suggested_price(bond, direction, market_data)
        quantity = self._calculate_suggested_quantity(bond, strategy, risk_score)
        
        # 计算预期收益
        expected_return = self._calculate_expected_return(bond, direction, price)
        
        # 生成决策原因
        reason = self._generate_decision_reason(bond, strategy, price_analysis, risk_score)
        
        return Decision(
            decision_id=decision_id,
            bond_code=bond.code,
            decision_type=decision_type,
            direction=direction,
            price=price,
            quantity=quantity,
            confidence=confidence,
            reason=reason,
            strategy_id=strategy.id,
            risk_score=risk_score,
            expected_return=expected_return
        )
    
    def _calculate_confidence(self, bond: Bond, strategy: Strategy, 
                            price_analysis: Dict[str, Any], risk_score: float) -> float:
        """计算决策置信度"""
        confidence = 0.5  # 基础置信度
        
        # 价格偏离度影响置信度
        deviation = abs(price_analysis.get('price_deviation', 0))
        if deviation > 0.05:  # 偏离超过5%
            confidence += 0.3
        elif deviation > 0.02:  # 偏离超过2%
            confidence += 0.2
        
        # 风险评分影响置信度（风险越低置信度越高）
        confidence += (1 - risk_score) * 0.3
        
        # 策略匹配度影响置信度
        if strategy.matches_bond(bond):
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _calculate_suggested_price(self, bond: Bond, direction: TradeDirection, 
                                 market_data: Optional[MarketData] = None) -> Optional[float]:
        """计算建议价格"""
        if direction == TradeDirection.HOLD:
            return None
        
        if market_data:
            if direction == TradeDirection.BUY and market_data.bid_price:
                return market_data.bid_price * 0.999  # 略低于买价
            elif direction == TradeDirection.SELL and market_data.ask_price:
                return market_data.ask_price * 1.001  # 略高于卖价
        
        return bond.current_price
    
    def _calculate_suggested_quantity(self, bond: Bond, strategy: Strategy, risk_score: float) -> Optional[float]:
        """计算建议数量"""
        base_quantity = 100000  # 基础数量
        
        # 根据风险调整数量
        risk_adjustment = 1 - risk_score
        adjusted_quantity = base_quantity * risk_adjustment
        
        # 根据策略最大持仓调整
        if strategy.max_position:
            adjusted_quantity = min(adjusted_quantity, strategy.max_position)
        
        return max(adjusted_quantity, 10000)  # 最小数量
    
    def _calculate_expected_return(self, bond: Bond, direction: TradeDirection, price: Optional[float]) -> Optional[float]:
        """计算预期收益"""
        if not price or not bond.yield_rate:
            return None
        
        if direction == TradeDirection.BUY:
            return bond.yield_rate * price
        elif direction == TradeDirection.SELL:
            return (price - bond.face_value) / bond.face_value
        
        return None
    
    def _generate_decision_reason(self, bond: Bond, strategy: Strategy, 
                                price_analysis: Dict[str, Any], risk_score: float) -> str:
        """生成决策原因"""
        reasons = []
        
        # 策略匹配
        reasons.append(f"匹配策略: {strategy.name}")
        
        # 价格分析
        deviation = price_analysis.get('price_deviation', 0)
        if abs(deviation) > 0.02:
            if deviation < 0:
                reasons.append(f"价格被低估 {abs(deviation)*100:.1f}%")
            else:
                reasons.append(f"价格被高估 {deviation*100:.1f}%")
        
        # 风险评估
        if risk_score < 0.3:
            reasons.append("风险较低")
        elif risk_score > 0.7:
            reasons.append("风险较高")
        
        # 收益率
        if bond.yield_rate:
            reasons.append(f"收益率: {bond.yield_rate*100:.2f}%")
        
        return "; ".join(reasons)
    
    def _create_wait_decision(self, bond_code: str, reason: str, strategy_id: Optional[str] = None) -> Decision:
        """创建等待决策"""
        return Decision(
            decision_id=str(uuid.uuid4()),
            bond_code=bond_code,
            decision_type=DecisionType.WAIT,
            direction=TradeDirection.HOLD,
            reason=reason,
            strategy_id=strategy_id
        )
    
    def _create_reject_decision(self, bond_code: str, reason: str, strategy_id: Optional[str] = None, 
                              risk_score: float = 0.0) -> Decision:
        """创建拒绝决策"""
        return Decision(
            decision_id=str(uuid.uuid4()),
            bond_code=bond_code,
            decision_type=DecisionType.REJECT,
            direction=TradeDirection.HOLD,
            reason=reason,
            strategy_id=strategy_id,
            risk_score=risk_score
        )
